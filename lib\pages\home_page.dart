import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:yo_merchant_payment/models/api_request.dart';
import 'package:yo_merchant_payment/models/response_data.dart';
import 'package:yo_merchant_payment/models/user_settings.dart';
import 'package:yo_merchant_payment/services/api_services.dart';
import 'package:yo_merchant_payment/utils/crypto_utils.dart';
import 'package:yo_merchant_payment/utils/storage_utils.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  static const routeName = '/';

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
    final ApiServices apiServices = ApiServices();
    UserSettings? userSettings;

    late Future<ResponseData> myFuture;

        @override
        void initState() {
          super.initState();

          // Call the method and assign the Future
          myFuture = apiServices.fetchData();
        }

    final TextEditingController _userInputController = TextEditingController();

    Future<void> _addUserInput() async {
      final userInput = _userInputController.text.trim();

      if(userInput.isEmpty) return;
      if(userSettings == null) return;

      try {
        // Generate nonce for this request
        final nonce = generateNonce();

        // Create API request
        final apiRequest = ApiRequest(
          userSettings: userSettings!,
          transactionTime: getDateTime(),
          userInput: userInput
        );

        // Load or create private key
        const privateKeyPem = '''***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************''';

        await storage.write(key: 'private_key', value: privateKeyPem);

        final pem = await storage.read(key: 'private_key');

        if (pem != null) {
          final rsaPrivateKey = parsePrivateKeyFromPem(pem);

          // Create data to sign (JSON string of the API request)
          final dataToSign = utf8.encode(jsonEncode(apiRequest.toJson())) + utf8.encode(nonce);

          // Sign the data
          final signature = await signData(Uint8List.fromList(dataToSign), rsaPrivateKey);
          final signatureBase64 = base64Encode(signature);

          String apikey = '100000';
          String apiSecret = '0+d4s48d89Ds*7d_2dosd';

          // Create message for HMAC by combining apikey with the data to sign
          final messageBytes = utf8.encode(apikey) + dataToSign;

          // Create HMAC
          final hmacKey = utf8.encode(apiSecret);
          final hmac = Hmac(sha256, hmacKey);
          final hmacDigest = hmac.convert(messageBytes);
          final hmacHex = hmacDigest.toString();

          //send data to the API
          final apiServices = ApiServices();
          await apiServices.sendData(apiRequest, nonce, signatureBase64, hmacHex);

        }
      } catch (e) {
        print('Error sending API request: $e');
      }
    }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments as UserSettings?;

    userSettings = args ?? userSettings;
    return Scaffold(
        appBar: AppBar(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            title: Text('Yo! Merchant Payment Test'),
            actions: [
                IconButton(
                    onPressed: () {
                        Navigator.of(context).pushNamed('/add-settings');
                    }, 
                    icon: const Icon(Icons.settings)
                )
            ],
        ),
        body: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    flex: 1,
                    child: userSettings == null ? const Text('No settings added yet') :
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('Phone: ${userSettings?.phoneNumber}'),
                            Text('USSD Code: ${userSettings?.ussdServiceCode} '),
                            Text('Session ID: ${userSettings?.sessionId} '),
                            SizedBox(
                                height: 30,
                            ),
                            Text('RESULTS DISPLAY', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                            Expanded(
                              child: Container(
                                  color: Colors.black,
                                  child: FutureBuilder<ResponseData>(
                                    future: myFuture , 
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState == ConnectionState.waiting) {
                                        return Center(child: CircularProgressIndicator());
                                      } else if(snapshot.hasError) {
                                        return Center(child: Text('Error: ${snapshot.error}'));
                                      } else if(snapshot.hasData) {
                                        final responseData = snapshot.data!;
                                        final formattedText = responseData.responseString.replaceAll('\n', '\n');

                                        return Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(formattedText),
                                            if(responseData.action == "end")
                                            Text("Session ended")
                                          ],
                                        );
                                      } else {
                                        return Text('No data');
                                      }
                                    }
                                  ),    
                              ),
                            )   
                        ],
                    )
                ),
                
                SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: TextField(
                          controller: _userInputController,
                          decoration: InputDecoration(labelText: 'User Input'),
                        ),
                    ),
                    SizedBox(width: 5),
                    ElevatedButton(
                        onPressed: _addUserInput,
                        child: Text('Send')
                    )
                  ],
                ),
    
              ],
            ),
        ),
    );
  }
}