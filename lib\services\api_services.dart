import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:yo_merchant_payment/models/api_request.dart';
import 'package:yo_merchant_payment/models/response_data.dart';

class ApiServices {

  Future<ResponseData> sendData(ApiRequest apiRequest, nonce, signatureBase64, hmacHex) async {
    final response =  await http.post(
      Uri.parse('https://devweb.yo.co.ug/172-31-28-190/zeroratedsitesimulator/execute'),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'x-ympfg-apikey': '100000',
        'x-ympfg-nonce':nonce,
        'x-ympfg-signature': signatureBase64,
        'x-ympfg-hmac-sha256': hmacHex
      },
      body: jsonEncode(apiRequest.toJson()),
    );

    print('Send data response status: ${response.statusCode}');
    print('Send data response body: ${response.body}');

    if (response.statusCode == 200 || response.statusCode == 201) {
      return ResponseData.fromJson(jsonDecode(response.body) as Map<String, dynamic>);
    } else {
      throw Exception('Failed to send data. Status code: ${response.statusCode}, Body: ${response.body}');
    }
  }

  Future<ResponseData> fetchData() async {
    try {
      final response = await http.get(
        Uri.parse('https://devweb.yo.co.ug/172-31-28-190/zeroratedsitesimulator/execute'),
      );

      print('Response status code: ${response.statusCode}');
      print('Response body: ${response.body}');


      if (response.statusCode == 200) {
        return ResponseData.fromJson(jsonDecode(response.body) as Map<String, dynamic>);
      } else {
        throw Exception('Failed to load Response Data. Status code: ${response.statusCode}, Body: ${response.body}');
      }
    } catch (e) {
      print('Network error in fetchData: $e');
      throw Exception('Network error: $e');
    }
  }
}